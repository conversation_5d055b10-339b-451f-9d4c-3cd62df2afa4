{"bmx-bumps_1": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "bmx-bumps_2": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "bmx-trees_1": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "bmx-trees_2": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "cat-girl_1": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "cat-girl_2": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "drift-straight": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "drone_1": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "drone_2": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "drone_3": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "drone_4": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "drone_5": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "gold-fish_1": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "gold-fish_2": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "gold-fish_3": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "gold-fish_4": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "gold-fish_5": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "schoolgirls_1": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "schoolgirls_2": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "schoolgirls_3": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "schoolgirls_4": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "schoolgirls_5": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "schoolgirls_6": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "schoolgirls_7": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "skate-park_1": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "skate-park_2": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "stunt_1": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "stunt_2": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "surf_1": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "surf_2": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "surf_3": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "swing": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "upside-down_1": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}, "upside-down_2": {"status": "error", "error": "Given groups=1, weight of size [32, 17, 7, 7], expected input[1, 20, 256, 320] to have 17 channels, but got 20 channels instead"}}