#!/usr/bin/env python3
"""
Test Inference Script for SDCNet with Masks
Performs inference on the entire test set and saves predictions for video comparison.
"""

import os
import sys
import argparse
import cv2
import numpy as np
from PIL import Image
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from tqdm import tqdm
import json
from datetime import datetime
import glob

# Add current directory to path
sys.path.append('.')

from models.sdc_net2d_with_masks import SDCNet2DWithMasks, SDCNet2DReconWithMasks
from datasets.frame_loader_with_masks import FrameLoaderWithMasks


def create_args():
    """Create arguments for the model and dataset"""
    args = argparse.Namespace()
    
    # Model parameters
    args.sequence_length = 2
    args.rgb_max = 255.0
    args.flownet2_checkpoint = './flownet2_pytorch/FlowNet2_checkpoint.pth.tar'
    
    # Dataset parameters
    args.sample_rate = 1
    args.crop_size = [256, 320]  # Height, Width
    args.start_index = 0
    args.stride = 64
    
    return args


def load_best_model(checkpoint_path, args, device):
    """Load the best trained model"""
    print(f"Loading model from: {checkpoint_path}")
    
    # Create model
    model = SDCNet2DReconWithMasks(args)
    
    # Load checkpoint
    if os.path.exists(checkpoint_path):
        checkpoint = torch.load(checkpoint_path, map_location=device)
        
        # Handle different checkpoint formats
        if 'state_dict' in checkpoint:
            state_dict = checkpoint['state_dict']
            epoch = checkpoint.get('epoch', 'unknown')
        else:
            state_dict = checkpoint
            epoch = 'unknown'
            
        model.load_state_dict(state_dict, strict=False)
        print(f"Successfully loaded checkpoint from epoch {epoch}")
    else:
        raise FileNotFoundError(f"Checkpoint not found: {checkpoint_path}")
    
    model.to(device)
    model.eval()
    return model


def find_latest_checkpoint(checkpoint_dir="./checkpoints"):
    """Find the latest best checkpoint"""
    # Look for best model checkpoints
    best_patterns = [
        os.path.join(checkpoint_dir, "*best*.pth"),
        os.path.join(checkpoint_dir, "*best*.pth.tar"),
        os.path.join(checkpoint_dir, "*.pth"),
        os.path.join(checkpoint_dir, "*.pth.tar")
    ]
    
    latest_checkpoint = None
    latest_time = 0
    
    for pattern in best_patterns:
        checkpoints = glob.glob(pattern)
        for ckpt in checkpoints:
            mtime = os.path.getmtime(ckpt)
            if mtime > latest_time:
                latest_time = mtime
                latest_checkpoint = ckpt
    
    return latest_checkpoint


def predict_video_sequence(model, video_data, device, args):
    """Predict all frames for a single video sequence"""
    predictions = []
    
    # Get video info
    video_name = video_data['video_name']
    frame_files = video_data['frames']
    mask_files = video_data['masks']
    target_files = video_data['targets']
    
    print(f"Processing video: {video_name} ({len(frame_files)} frames)")
    
    # Process video in sliding window fashion
    for i in tqdm(range(len(frame_files) - args.sequence_length), 
                  desc=f"Predicting {video_name}", leave=False):
        
        # Get sequence of frames and masks
        sequence_frames = []
        sequence_masks = []
        
        for j in range(args.sequence_length + 1):  # +1 for target
            frame_idx = i + j
            
            # Load frame
            frame = cv2.imread(frame_files[frame_idx])[..., :3]  # RGB
            frame = torch.from_numpy(frame.transpose(2, 0, 1)).float()
            sequence_frames.append(frame)
            
            # Load mask
            mask = cv2.imread(mask_files[frame_idx], cv2.IMREAD_GRAYSCALE)
            mask = np.where(mask > 127, 255, 0).astype(np.uint8)
            mask = torch.from_numpy(mask).float().unsqueeze(0)
            sequence_masks.append(mask)
        
        # Create input dict
        input_dict = {
            'image': [frame.unsqueeze(0).to(device) for frame in sequence_frames],
            'mask': [mask.unsqueeze(0).to(device) for mask in sequence_masks]
        }
        
        # Perform inference
        with torch.no_grad():
            losses, prediction, target = model(input_dict)
        
        # Convert prediction to numpy
        pred_np = prediction.cpu().squeeze().numpy().transpose(1, 2, 0)
        pred_np = np.clip(pred_np, 0, 255).astype(np.uint8)
        
        predictions.append({
            'frame_idx': i + args.sequence_length,  # Index of predicted frame
            'prediction': pred_np,
            'target_file': target_files[i + args.sequence_length],
            'losses': {k: v.item() if torch.is_tensor(v) else v for k, v in losses.items()}
        })
    
    return predictions


def save_predictions(predictions, video_name, output_dir):
    """Save predictions for a video"""
    video_output_dir = os.path.join(output_dir, video_name)
    os.makedirs(video_output_dir, exist_ok=True)
    
    # Save individual frames
    for pred_data in predictions:
        frame_idx = pred_data['frame_idx']
        prediction = pred_data['prediction']
        
        # Save prediction
        pred_filename = os.path.join(video_output_dir, f"pred_{frame_idx:05d}.png")
        cv2.imwrite(pred_filename, prediction)
    
    # Save metadata
    metadata = {
        'video_name': video_name,
        'num_predictions': len(predictions),
        'frame_indices': [p['frame_idx'] for p in predictions],
        'average_losses': {}
    }
    
    # Calculate average losses
    if predictions:
        loss_keys = predictions[0]['losses'].keys()
        for key in loss_keys:
            avg_loss = np.mean([p['losses'][key] for p in predictions])
            metadata['average_losses'][key] = float(avg_loss)
    
    metadata_file = os.path.join(video_output_dir, 'metadata.json')
    with open(metadata_file, 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"Saved {len(predictions)} predictions for {video_name}")
    return video_output_dir


def main():
    parser = argparse.ArgumentParser(description='Test Inference for SDCNet with Masks')
    parser.add_argument('--test_dir', default='./dataset/test', 
                       help='Path to test dataset directory')
    parser.add_argument('--checkpoint', default=None,
                       help='Path to model checkpoint (auto-detect if not provided)')
    parser.add_argument('--output_dir', default='./test_results',
                       help='Directory to save predictions')
    parser.add_argument('--gpu', type=int, default=0,
                       help='GPU device ID')
    parser.add_argument('--batch_size', type=int, default=1,
                       help='Batch size for inference')
    
    cmd_args = parser.parse_args()
    
    # Setup device
    device = torch.device(f'cuda:{cmd_args.gpu}' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create model args
    model_args = create_args()
    
    # Find checkpoint if not provided
    if cmd_args.checkpoint is None:
        cmd_args.checkpoint = find_latest_checkpoint()
        if cmd_args.checkpoint is None:
            raise FileNotFoundError("No checkpoint found. Please specify --checkpoint")
    
    print(f"Using checkpoint: {cmd_args.checkpoint}")
    
    # Load model
    model = load_best_model(cmd_args.checkpoint, model_args, device)
    
    # Create output directory
    timestamp = datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
    output_dir = os.path.join(cmd_args.output_dir, f"inference_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    
    # Save configuration
    config = {
        'timestamp': timestamp,
        'checkpoint': cmd_args.checkpoint,
        'test_dir': cmd_args.test_dir,
        'device': str(device),
        'model_args': vars(model_args)
    }
    
    config_file = os.path.join(output_dir, 'inference_config.json')
    with open(config_file, 'w') as f:
        json.dump(config, f, indent=2)
    
    # Load test dataset
    print("Loading test dataset...")
    test_dataset = FrameLoaderWithMasks(model_args, cmd_args.test_dir, is_training=False)
    
    print(f"Found {len(test_dataset.ref)} videos in test set")
    
    # Process each video
    all_results = {}
    
    for video_idx, video_data in enumerate(test_dataset.ref):
        video_name = video_data['video_name']
        print(f"\nProcessing video {video_idx + 1}/{len(test_dataset.ref)}: {video_name}")
        
        try:
            # Generate predictions for this video
            predictions = predict_video_sequence(model, video_data, device, model_args)
            
            # Save predictions
            video_output_dir = save_predictions(predictions, video_name, output_dir)
            
            all_results[video_name] = {
                'output_dir': video_output_dir,
                'num_predictions': len(predictions),
                'status': 'success'
            }
            
        except Exception as e:
            print(f"Error processing video {video_name}: {str(e)}")
            all_results[video_name] = {
                'status': 'error',
                'error': str(e)
            }
    
    # Save overall results
    results_file = os.path.join(output_dir, 'inference_results.json')
    with open(results_file, 'w') as f:
        json.dump(all_results, f, indent=2)
    
    print(f"\nInference completed!")
    print(f"Results saved to: {output_dir}")
    print(f"Successfully processed: {sum(1 for r in all_results.values() if r['status'] == 'success')} videos")
    print(f"Failed: {sum(1 for r in all_results.values() if r['status'] == 'error')} videos")


if __name__ == "__main__":
    main()
